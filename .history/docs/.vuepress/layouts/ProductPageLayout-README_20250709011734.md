# ProductPageLayout.vue 使用说明

## 概述

ProductPageLayout.vue 是为VuePress 2.x项目优化的产品页面模板，专门设计用于展示产品信息，具有简洁的设计和良好的响应式效果。

## 页面结构

### 1. 产品标题区域
- **中文标题**：主要产品名称，使用超大字体（--font-size-4xl）突出显示
- **英文标题**：副标题，提供英文描述，字体较大（--font-size-xl）
- **设计特点**：使用品牌渐变色背景，添加文字阴影效果，增强视觉冲击力

### 2. 产品图片展示区域
- **主要产品图片**：超大尺寸展示图片（最大宽度95%，桌面端可达1000px）
- **产品列表**：支持多个产品的列表展示，图片尺寸优化（桌面端220x220px）
- **响应式设计**：自适应不同屏幕尺寸，移动端垂直布局
- **交互效果**：悬停缩放效果，增强用户体验

### 3. 详细内容区域（可选）
- **Markdown内容**：支持完整的Markdown格式
- **可控制显示**：通过配置控制是否显示

## Frontmatter 配置

```yaml
---
layout: ProductPageLayout
pageTitle: 页面标题
productTitleCn: 中文产品标题
productTitleEn: English Product Title
heroImage:
  src: 主要产品图片URL（建议高分辨率图片）
  alt: 图片描述
productList:
  - titleCn: 产品1中文名称
    titleEn: Product 1 English Name
    image: 产品1图片URL（建议正方形比例）
  - titleCn: 产品2中文名称
    titleEn: Product 2 English Name
    image: 产品2图片URL（建议正方形比例）
backUrl: /products/
showDetailContent: true
showMoreContent: true
moreContentUrl: /products/
---
```

## 配置参数说明

### 必需参数
- `layout: ProductPageLayout` - 指定使用产品页面布局
- `productTitleCn` - 中文产品标题（将以超大字体显示）
- `productTitleEn` - 英文产品标题（副标题）

### 可选参数
- `pageTitle` - 页面标题（用于浏览器标题栏）
- `heroImage` - 主要展示图片（推荐使用高分辨率图片）
  - `src` - 图片URL（建议使用HTTPS）
  - `alt` - 图片描述
- `productList` - 产品列表数组
  - `titleCn` - 产品中文名称
  - `titleEn` - 产品英文名称
  - `image` - 产品图片URL（建议正方形比例，最小200x200px）
- `backUrl` - 返回按钮链接（默认：/products/）
- `showDetailContent` - 是否显示详细内容区域（默认：true）
- `showMoreContent` - 是否显示"更多内容"按钮（默认：false）
- `moreContentUrl` - "更多内容"按钮链接

## 响应式设计

### 桌面端（≥768px）
- 产品列表项使用水平布局
- 图片尺寸：150x150px
- 较大的字体和间距

### 移动端（<480px）
- 产品列表项使用垂直布局
- 图片尺寸：100x100px
- 紧凑的布局和字体

## 样式特点

### 设计原则
- **简洁设计**：避免过多文字描述，突出产品展示
- **品牌一致性**：使用双马智能科技品牌色彩
- **用户体验**：清晰的层次结构和导航

### 视觉效果
- **渐变背景**：标题区域使用品牌渐变色
- **阴影效果**：卡片和图片使用适度阴影
- **悬停效果**：产品项支持悬停动画
- **圆角设计**：统一的圆角风格

## 使用示例

参考 `docs/products/new/README.md` 文件，该文件展示了完整的配置示例。

## 技术要求

- VuePress 2.x
- Vue 3.x
- SCSS支持
- 响应式设计支持

## 浏览器兼容性

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 移动端浏览器
- 支持CSS Grid和Flexbox

## 注意事项

1. 图片URL应使用HTTPS协议
2. 图片建议使用适当的尺寸以优化加载速度
3. 产品列表建议不超过10个项目以保持页面性能
4. 中英文标题应保持简洁，避免过长文本
