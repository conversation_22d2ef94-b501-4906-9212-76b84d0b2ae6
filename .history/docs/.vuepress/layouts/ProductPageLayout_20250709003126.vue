<template>
  <div class="mobile-page product-page">
    <!-- 移动端导航头部 -->
    <MobileHeader 
      :title="pageTitle"
      :show-back="true"
      :back-url="backUrl"
      :show-logo="false"
      :show-home="true"
    />
    
    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 主要内容区域 -->
      <section class="main-content">
        <div class="mobile-container">
          <!-- 内容卡片 -->
          <div class="mobile-content card">
            <!-- 内容头部 -->
            <div class="content-header">
              {{ contentHeader }}
            </div>
            
            <!-- 内容主体 -->
            <div class="content-body">
              <!-- 产品图片区域 -->
              <div v-if="heroImage" class="hero-image">
                <img
                  :src="heroImage.src"
                  :alt="heroImage.alt"
                  class="responsive-image"
                />
                <!-- 图片标题 -->
                <div v-if="heroImageTitle" class="image-title">
                  {{ heroImageTitle }}
                </div>
              </div>

              <!-- 插槽：自定义内容 -->
              <slot name="content">
                <!-- 默认内容区域 -->
                <div class="content-section">
                  <Content />
                </div>
              </slot>
            </div>
          </div>
        </div>
      </section>
    </div>
    
    <!-- 页脚 -->
    <MobileFooter 
      :show-back-to-top="true"
      :show-more-content="showMoreContent"
      :more-content-url="moreContentUrl"
      :quick-links="footerLinks"
      :copyright-text="copyrightText"
      :company-info="companyInfo"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Content, usePageData, usePageFrontmatter } from 'vuepress/client'
import MobileFooter from '../components/MobileFooter.vue'
import MobileHeader from '../components/MobileHeader.vue'

// 获取页面数据
const page = usePageData()
const frontmatter = usePageFrontmatter()

// Props定义
const props = defineProps({
  // 页面标题
  pageTitle: {
    type: String,
    default: ''
  },
  // 内容头部标题
  contentHeader: {
    type: String,
    default: ''
  },
  // 返回URL
  backUrl: {
    type: String,
    default: '/products/'
  },
  // 英雄图片
  heroImage: {
    type: Object,
    default: null
    // 格式: { src: 'url', alt: 'description' }
  },
  // 是否显示更多内容按钮
  showMoreContent: {
    type: Boolean,
    default: false
  },
  // 更多内容URL
  moreContentUrl: {
    type: String,
    default: '/articles/'
  },
  // 页脚快速链接
  footerLinks: {
    type: Array,
    default: () => [
      { text: '返回首页', url: '/' },
      { text: '产品展示', url: '/products/' },
      { text: '生产车间', url: '/workshop/' },
      { text: '资质证书', url: '/qualification/' }
    ]
  },
  // 版权文本
  copyrightText: {
    type: String,
    default: '© 2022-2023 SPINRED 版权所有'
  },
  // 公司信息
  companyInfo: {
    type: String,
    default: '无锡市双马智能科技有限公司'
  }
})

// 计算属性：获取页面标题
const pageTitle = computed(() => {
  return frontmatter.value.pageTitle || props.pageTitle || page.value.title
})

// 计算属性：获取内容头部标题
const contentHeader = computed(() => {
  return frontmatter.value.contentHeader || props.contentHeader || pageTitle.value
})

// 计算属性：获取其他 frontmatter 数据
const heroImage = computed(() => frontmatter.value.heroImage)
const heroImageTitle = computed(() => {
  const image = frontmatter.value.heroImage
  return image?.title || image?.alt || ''
})
const backUrl = computed(() => frontmatter.value.backUrl || props.backUrl)
const showMoreContent = computed(() => frontmatter.value.showMoreContent || props.showMoreContent)
const moreContentUrl = computed(() => frontmatter.value.moreContentUrl || props.moreContentUrl)
</script>

<style lang="scss" scoped>
.product-page {
  background: var(--brand-gray);
  min-height: 100vh;
}

.main-content {
  padding: var(--spacing-6) 0;
  
  .mobile-content {
    margin-bottom: var(--spacing-8);
    
    .content-header {
      background: var(--gradient-primary);
      color: var(--brand-white);
      padding: var(--spacing-4);
      border-radius: var(--radius-lg) var(--radius-lg) 0 0;
      text-align: center;
      font-weight: 600;
      font-size: var(--font-size-lg);
    }
    
    .content-body {
      padding: var(--spacing-6);
      line-height: 1.8;
      
      .hero-image {
        margin-bottom: var(--spacing-6);
        text-align: center;

        .responsive-image {
          width: 100%;
          max-width: 400px;
          height: auto;
          border-radius: var(--radius-md);
          box-shadow: var(--shadow-md);
          margin-bottom: var(--spacing-3);
        }

        .image-title {
          font-size: var(--font-size-sm);
          color: #666;
          font-style: italic;
          margin-top: var(--spacing-2);
          line-height: 1.4;
        }
      }
      
      .content-section {
        margin-bottom: var(--spacing-6);
        
        &:last-child {
          margin-bottom: 0;
        }
        
        p {
          margin-bottom: var(--spacing-4);
          color: #555;
          text-align: justify;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
        
        h1, h2, h3, h4, h5, h6 {
          color: var(--brand-dark-gray);
          margin-top: var(--spacing-6);
          margin-bottom: var(--spacing-4);
          
          &:first-child {
            margin-top: 0;
          }
        }
        
        ul, ol {
          margin-bottom: var(--spacing-4);
          padding-left: var(--spacing-6);
          
          li {
            margin-bottom: var(--spacing-2);
            color: #555;
          }
        }
      }
    }
  }
}

// 响应式调整
@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-4) 0;
    
    .mobile-content {
      .content-body {
        padding: var(--spacing-4);
        
        .hero-image {
          margin-bottom: var(--spacing-4);
        }
        
        .content-section {
          margin-bottom: var(--spacing-4);
        }
      }
    }
  }
}

// 进入动画
.main-content {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
