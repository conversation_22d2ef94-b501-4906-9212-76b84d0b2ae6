import { defineClientConfig } from 'vuepress/client'
import Article from './layouts/Article.vue'
import Category from './layouts/Category.vue'
import CompanyProfile from './layouts/CompanyProfile.vue'
import Home from './layouts/Home.vue'
import ProductDisplay from './layouts/ProductDisplay.vue'
import Tag from './layouts/Tag.vue'
import Timeline from './layouts/Timeline.vue'

// 导入组件
import MobileFooter from './components/MobileFooter.vue'
import MobileHeader from './components/MobileHeader.vue'
import NavigationCard from './components/NavigationCard.vue'

// 导入样式
import './styles/index.scss'
import './styles/mobile.scss'

export default defineClientConfig({
  // 注册布局组件
  layouts: {
    Article,
    Category,
    Tag,
    Timeline,
    Home,
    CompanyProfile,
    ProductDisplay,
  },

  enhance({ app, router, siteData }) {
    // 注册全局组件
    app.component('MobileHeader', MobileHeader)
    app.component('NavigationCard', NavigationCard)
    app.component('MobileFooter', MobileFooter)
  },
})
