import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/.vuepress/layouts/ProductPageLayout-README.html.vue"
const data = JSON.parse("{\"path\":\"/.vuepress/layouts/ProductPageLayout-README.html\",\"title\":\"ProductPageLayout.vue 使用说明\",\"lang\":\"zh-CN\",\"frontmatter\":{},\"headers\":[{\"level\":2,\"title\":\"概述\",\"slug\":\"概述\",\"link\":\"#概述\",\"children\":[]},{\"level\":2,\"title\":\"页面结构\",\"slug\":\"页面结构\",\"link\":\"#页面结构\",\"children\":[{\"level\":3,\"title\":\"1. 产品标题区域\",\"slug\":\"_1-产品标题区域\",\"link\":\"#_1-产品标题区域\",\"children\":[]},{\"level\":3,\"title\":\"2. 产品图片展示区域\",\"slug\":\"_2-产品图片展示区域\",\"link\":\"#_2-产品图片展示区域\",\"children\":[]},{\"level\":3,\"title\":\"3. 详细内容区域（可选）\",\"slug\":\"_3-详细内容区域-可选\",\"link\":\"#_3-详细内容区域-可选\",\"children\":[]}]},{\"level\":2,\"title\":\"Frontmatter 配置\",\"slug\":\"frontmatter-配置\",\"link\":\"#frontmatter-配置\",\"children\":[]},{\"level\":2,\"title\":\"配置参数说明\",\"slug\":\"配置参数说明\",\"link\":\"#配置参数说明\",\"children\":[{\"level\":3,\"title\":\"必需参数\",\"slug\":\"必需参数\",\"link\":\"#必需参数\",\"children\":[]},{\"level\":3,\"title\":\"可选参数\",\"slug\":\"可选参数\",\"link\":\"#可选参数\",\"children\":[]}]},{\"level\":2,\"title\":\"响应式设计\",\"slug\":\"响应式设计\",\"link\":\"#响应式设计\",\"children\":[{\"level\":3,\"title\":\"大屏幕（≥1200px）\",\"slug\":\"大屏幕-≥1200px\",\"link\":\"#大屏幕-≥1200px\",\"children\":[]},{\"level\":3,\"title\":\"桌面端（768px-1199px）\",\"slug\":\"桌面端-768px-1199px\",\"link\":\"#桌面端-768px-1199px\",\"children\":[]},{\"level\":3,\"title\":\"移动端（<480px）\",\"slug\":\"移动端-480px\",\"link\":\"#移动端-480px\",\"children\":[]}]},{\"level\":2,\"title\":\"样式特点\",\"slug\":\"样式特点\",\"link\":\"#样式特点\",\"children\":[{\"level\":3,\"title\":\"设计原则\",\"slug\":\"设计原则\",\"link\":\"#设计原则\",\"children\":[]},{\"level\":3,\"title\":\"视觉效果\",\"slug\":\"视觉效果\",\"link\":\"#视觉效果\",\"children\":[]}]},{\"level\":2,\"title\":\"使用示例\",\"slug\":\"使用示例\",\"link\":\"#使用示例\",\"children\":[]},{\"level\":2,\"title\":\"技术要求\",\"slug\":\"技术要求\",\"link\":\"#技术要求\",\"children\":[]},{\"level\":2,\"title\":\"浏览器兼容性\",\"slug\":\"浏览器兼容性\",\"link\":\"#浏览器兼容性\",\"children\":[]},{\"level\":2,\"title\":\"注意事项\",\"slug\":\"注意事项\",\"link\":\"#注意事项\",\"children\":[]}],\"git\":{},\"filePathRelative\":\".vuepress/layouts/ProductPageLayout-README.md\",\"excerpt\":\"\\n<h2>概述</h2>\\n<p>ProductPageLayout.vue 是为VuePress 2.x项目优化的产品页面模板，专门设计用于展示产品信息，具有简洁的设计和良好的响应式效果。</p>\\n<h2>页面结构</h2>\\n<h3>1. 产品标题区域</h3>\\n<ul>\\n<li><strong>中文标题</strong>：主要产品名称，使用超大字体（--font-size-4xl）突出显示</li>\\n<li><strong>英文标题</strong>：副标题，提供英文描述，字体较大（--font-size-xl）</li>\\n<li><strong>设计特点</strong>：使用品牌渐变色背景，添加文字阴影效果，增强视觉冲击力</li>\\n</ul>\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
