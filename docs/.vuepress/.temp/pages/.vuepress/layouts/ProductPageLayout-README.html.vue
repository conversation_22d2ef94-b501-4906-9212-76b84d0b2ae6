<template><div><h1 id="productpagelayout-vue-使用说明" tabindex="-1"><a class="header-anchor" href="#productpagelayout-vue-使用说明"><span>ProductPageLayout.vue 使用说明</span></a></h1>
<h2 id="概述" tabindex="-1"><a class="header-anchor" href="#概述"><span>概述</span></a></h2>
<p>ProductPageLayout.vue 是为VuePress 2.x项目优化的产品页面模板，专门设计用于展示产品信息，具有简洁的设计和良好的响应式效果。</p>
<h2 id="页面结构" tabindex="-1"><a class="header-anchor" href="#页面结构"><span>页面结构</span></a></h2>
<h3 id="_1-产品标题区域" tabindex="-1"><a class="header-anchor" href="#_1-产品标题区域"><span>1. 产品标题区域</span></a></h3>
<ul>
<li><strong>中文标题</strong>：主要产品名称，使用超大字体（--font-size-4xl）突出显示</li>
<li><strong>英文标题</strong>：副标题，提供英文描述，字体较大（--font-size-xl）</li>
<li><strong>设计特点</strong>：使用品牌渐变色背景，添加文字阴影效果，增强视觉冲击力</li>
</ul>
<h3 id="_2-产品图片展示区域" tabindex="-1"><a class="header-anchor" href="#_2-产品图片展示区域"><span>2. 产品图片展示区域</span></a></h3>
<ul>
<li><strong>主要产品图片</strong>：超大尺寸展示图片（最大宽度95%，桌面端可达1000px）</li>
<li><strong>产品列表</strong>：支持多个产品的列表展示，图片尺寸优化（桌面端220x220px）</li>
<li><strong>响应式设计</strong>：自适应不同屏幕尺寸，移动端垂直布局</li>
<li><strong>交互效果</strong>：悬停缩放效果，增强用户体验</li>
</ul>
<h3 id="_3-详细内容区域-可选" tabindex="-1"><a class="header-anchor" href="#_3-详细内容区域-可选"><span>3. 详细内容区域（可选）</span></a></h3>
<ul>
<li><strong>Markdown内容</strong>：支持完整的Markdown格式</li>
<li><strong>可控制显示</strong>：通过配置控制是否显示</li>
</ul>
<h2 id="frontmatter-配置" tabindex="-1"><a class="header-anchor" href="#frontmatter-配置"><span>Frontmatter 配置</span></a></h2>
<div class="language-yaml line-numbers-mode" data-highlighter="prismjs" data-ext="yml"><pre v-pre><code><span class="line"><span class="token punctuation">---</span></span>
<span class="line"><span class="token key atrule">layout</span><span class="token punctuation">:</span> ProductPageLayout</span>
<span class="line"><span class="token key atrule">pageTitle</span><span class="token punctuation">:</span> 页面标题</span>
<span class="line"><span class="token key atrule">productTitleCn</span><span class="token punctuation">:</span> 中文产品标题</span>
<span class="line"><span class="token key atrule">productTitleEn</span><span class="token punctuation">:</span> English Product Title</span>
<span class="line"><span class="token key atrule">heroImage</span><span class="token punctuation">:</span></span>
<span class="line">  <span class="token key atrule">src</span><span class="token punctuation">:</span> 主要产品图片URL（建议高分辨率图片）</span>
<span class="line">  <span class="token key atrule">alt</span><span class="token punctuation">:</span> 图片描述</span>
<span class="line"><span class="token key atrule">productList</span><span class="token punctuation">:</span></span>
<span class="line">  <span class="token punctuation">-</span> <span class="token key atrule">titleCn</span><span class="token punctuation">:</span> 产品1中文名称</span>
<span class="line">    <span class="token key atrule">titleEn</span><span class="token punctuation">:</span> Product 1 English Name</span>
<span class="line">    <span class="token key atrule">image</span><span class="token punctuation">:</span> 产品1图片URL（建议正方形比例）</span>
<span class="line">  <span class="token punctuation">-</span> <span class="token key atrule">titleCn</span><span class="token punctuation">:</span> 产品2中文名称</span>
<span class="line">    <span class="token key atrule">titleEn</span><span class="token punctuation">:</span> Product 2 English Name</span>
<span class="line">    <span class="token key atrule">image</span><span class="token punctuation">:</span> 产品2图片URL（建议正方形比例）</span>
<span class="line"><span class="token key atrule">productFeatures</span><span class="token punctuation">:</span></span>
<span class="line">  <span class="token punctuation">-</span> 高清液晶显示屏，清晰可见</span>
<span class="line">  <span class="token punctuation">-</span> 蓝牙4.0连接，稳定可靠</span>
<span class="line">  <span class="token punctuation">-</span> IP67防水防尘，适应恶劣环境</span>
<span class="line">  <span class="token punctuation">-</span> 超长续航，低功耗设计</span>
<span class="line">  <span class="token punctuation">-</span> 智能防盗系统，安全保障</span>
<span class="line"><span class="token key atrule">backUrl</span><span class="token punctuation">:</span> /products/</span>
<span class="line"><span class="token key atrule">showDetailContent</span><span class="token punctuation">:</span> <span class="token boolean important">true</span></span>
<span class="line"><span class="token key atrule">showMoreContent</span><span class="token punctuation">:</span> <span class="token boolean important">true</span></span>
<span class="line"><span class="token key atrule">moreContentUrl</span><span class="token punctuation">:</span> /products/</span>
<span class="line"><span class="token punctuation">---</span></span>
<span class="line"></span></code></pre>
<div class="line-numbers" aria-hidden="true" style="counter-reset:line-number 0"><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div><div class="line-number"></div></div></div><h2 id="配置参数说明" tabindex="-1"><a class="header-anchor" href="#配置参数说明"><span>配置参数说明</span></a></h2>
<h3 id="必需参数" tabindex="-1"><a class="header-anchor" href="#必需参数"><span>必需参数</span></a></h3>
<ul>
<li><code v-pre>layout: ProductPageLayout</code> - 指定使用产品页面布局</li>
<li><code v-pre>productTitleCn</code> - 中文产品标题（将以超大字体显示）</li>
<li><code v-pre>productTitleEn</code> - 英文产品标题（副标题）</li>
</ul>
<h3 id="可选参数" tabindex="-1"><a class="header-anchor" href="#可选参数"><span>可选参数</span></a></h3>
<ul>
<li><code v-pre>pageTitle</code> - 页面标题（用于浏览器标题栏）</li>
<li><code v-pre>heroImage</code> - 主要展示图片（推荐使用高分辨率图片）
<ul>
<li><code v-pre>src</code> - 图片URL（建议使用HTTPS）</li>
<li><code v-pre>alt</code> - 图片描述</li>
</ul>
</li>
<li><code v-pre>productList</code> - 产品列表数组
<ul>
<li><code v-pre>titleCn</code> - 产品中文名称</li>
<li><code v-pre>titleEn</code> - 产品英文名称</li>
<li><code v-pre>image</code> - 产品图片URL（建议正方形比例，最小200x200px）</li>
</ul>
</li>
<li><code v-pre>productFeatures</code> - 产品特点列表数组
<ul>
<li>格式：字符串数组，每个元素为一个产品特点描述</li>
<li>建议3-5个特点，每个特点描述简洁明了（不超过20个字符）</li>
</ul>
</li>
<li><code v-pre>backUrl</code> - 返回按钮链接（默认：/products/）</li>
<li><code v-pre>showDetailContent</code> - 是否显示详细内容区域（默认：true）</li>
<li><code v-pre>showMoreContent</code> - 是否显示&quot;更多内容&quot;按钮（默认：false）</li>
<li><code v-pre>moreContentUrl</code> - &quot;更多内容&quot;按钮链接</li>
</ul>
<h2 id="响应式设计" tabindex="-1"><a class="header-anchor" href="#响应式设计"><span>响应式设计</span></a></h2>
<h3 id="大屏幕-≥1200px" tabindex="-1"><a class="header-anchor" href="#大屏幕-≥1200px"><span>大屏幕（≥1200px）</span></a></h3>
<ul>
<li>主要产品图片最大宽度：1000px</li>
<li>产品列表项图片尺寸：220x220px</li>
<li>产品标题使用--font-size-4xl</li>
</ul>
<h3 id="桌面端-768px-1199px" tabindex="-1"><a class="header-anchor" href="#桌面端-768px-1199px"><span>桌面端（768px-1199px）</span></a></h3>
<ul>
<li>主要产品图片最大宽度：900px</li>
<li>产品列表项使用水平布局，图片尺寸：200x200px</li>
<li>产品标题使用--font-size-4xl</li>
</ul>
<h3 id="移动端-480px" tabindex="-1"><a class="header-anchor" href="#移动端-480px"><span>移动端（&lt;480px）</span></a></h3>
<ul>
<li>主要产品图片占满宽度（100%）</li>
<li>产品列表项使用垂直布局，图片尺寸：140x140px</li>
<li>产品标题使用--font-size-3xl</li>
<li>紧凑的布局和间距</li>
</ul>
<h2 id="样式特点" tabindex="-1"><a class="header-anchor" href="#样式特点"><span>样式特点</span></a></h2>
<h3 id="设计原则" tabindex="-1"><a class="header-anchor" href="#设计原则"><span>设计原则</span></a></h3>
<ul>
<li><strong>简洁设计</strong>：避免过多文字描述，突出产品展示</li>
<li><strong>品牌一致性</strong>：使用双马智能科技品牌色彩</li>
<li><strong>用户体验</strong>：清晰的层次结构和导航</li>
</ul>
<h3 id="视觉效果" tabindex="-1"><a class="header-anchor" href="#视觉效果"><span>视觉效果</span></a></h3>
<ul>
<li><strong>渐变背景</strong>：标题区域使用品牌渐变色</li>
<li><strong>文字阴影</strong>：主标题添加文字阴影效果，增强视觉层次</li>
<li><strong>阴影效果</strong>：卡片和图片使用多层次阴影（--shadow-lg, --shadow-xl）</li>
<li><strong>悬停效果</strong>：主图片和产品项支持缩放动画（scale 1.03/1.05）</li>
<li><strong>圆角设计</strong>：统一的圆角风格，主图片使用--radius-xl</li>
<li><strong>过渡动画</strong>：所有交互元素都有流畅的过渡效果（0.3s-0.4s）</li>
</ul>
<h2 id="使用示例" tabindex="-1"><a class="header-anchor" href="#使用示例"><span>使用示例</span></a></h2>
<p>参考 <code v-pre>docs/products/new/README.md</code> 文件，该文件展示了完整的配置示例。</p>
<h2 id="技术要求" tabindex="-1"><a class="header-anchor" href="#技术要求"><span>技术要求</span></a></h2>
<ul>
<li>VuePress 2.x</li>
<li>Vue 3.x</li>
<li>SCSS支持</li>
<li>响应式设计支持</li>
</ul>
<h2 id="浏览器兼容性" tabindex="-1"><a class="header-anchor" href="#浏览器兼容性"><span>浏览器兼容性</span></a></h2>
<ul>
<li>现代浏览器（Chrome、Firefox、Safari、Edge）</li>
<li>移动端浏览器</li>
<li>支持CSS Grid和Flexbox</li>
</ul>
<h2 id="注意事项" tabindex="-1"><a class="header-anchor" href="#注意事项"><span>注意事项</span></a></h2>
<ol>
<li>
<p><strong>图片要求</strong>：</p>
<ul>
<li>主要产品图片建议使用高分辨率图片（最小800x600px）</li>
<li>产品列表图片建议使用正方形比例（最小200x200px）</li>
<li>所有图片URL应使用HTTPS协议</li>
<li>建议使用WebP格式以优化加载速度</li>
</ul>
</li>
<li>
<p><strong>性能优化</strong>：</p>
<ul>
<li>产品列表建议不超过20个项目以保持页面性能</li>
<li>图片应进行适当压缩，建议单张图片不超过500KB</li>
</ul>
</li>
<li>
<p><strong>内容建议</strong>：</p>
<ul>
<li>中文标题应保持简洁有力，建议不超过10个字符</li>
<li>英文标题建议不超过30个字符</li>
<li>产品名称避免过长，影响布局美观</li>
</ul>
</li>
<li>
<p><strong>用户体验</strong>：</p>
<ul>
<li>主要产品图片应清晰展示产品细节</li>
<li>产品列表图片应保持一致的风格和质量</li>
<li>确保所有图片都有适当的alt属性以提升可访问性</li>
</ul>
</li>
</ol>
</div></template>


