import comp from "/Users/<USER>/code/work/shuangma/portal/docs/.vuepress/.temp/pages/test-product-features.html.vue"
const data = JSON.parse("{\"path\":\"/test-product-features.html\",\"title\":\"产品特点测试页面\",\"lang\":\"zh-CN\",\"frontmatter\":{\"layout\":\"ProductPageLayout\",\"pageTitle\":\"产品特点测试页面\",\"productTitleCn\":\"产品特点测试\",\"productTitleEn\":\"Product Features Test\",\"productList\":[{\"titleCn\":\"测试产品1\",\"titleEn\":\"Test Product 1\",\"image\":\"https://img-public.hui1688.cn/shuangma/66040eece3f7a.png\",\"features\":[\"特点1：高清显示\",\"特点2：蓝牙连接\",\"特点3：防水防尘\"]},{\"primaryTitle\":\"测试产品2\",\"titleCn\":\"带主标题的产品\",\"titleEn\":\"Product with Primary Title\",\"image\":\"https://img-public.hui1688.cn/shuangma/66040eb46ddcb.png\",\"features\":[\"智能控制系统\",\"NFC功能\",\"超长续航\",\"一键启动\",\"语音控制\"]},{\"titleCn\":\"无特点产品\",\"titleEn\":\"Product without Features\",\"image\":\"https://img-public.hui1688.cn/shuangma/product-lcd.jpg\"}],\"backUrl\":\"/products/\",\"showDetailContent\":false},\"headers\":[{\"level\":2,\"title\":\"测试内容\",\"slug\":\"测试内容\",\"link\":\"#测试内容\",\"children\":[]},{\"level\":2,\"title\":\"预期效果\",\"slug\":\"预期效果\",\"link\":\"#预期效果\",\"children\":[]}],\"git\":{},\"filePathRelative\":\"test-product-features.md\",\"excerpt\":\"\\n<p>这是一个用于测试ProductPageLayout.vue组件中每个产品项独立特点显示功能的测试页面。</p>\\n<h2>测试内容</h2>\\n<ol>\\n<li><strong>测试产品1</strong> - 包含3个特点</li>\\n<li><strong>测试产品2</strong> - 包含5个特点，并有主标题</li>\\n<li><strong>无特点产品</strong> - 没有features字段，不应显示特点列表</li>\\n</ol>\\n<h2>预期效果</h2>\\n<ul>\\n<li>每个产品的特点应该显示在产品标题下方</li>\\n<li>特点列表应该有合适的样式和图标</li>\\n<li>没有特点的产品不应显示特点区域</li>\\n<li>在不同屏幕尺寸下都应该有良好的显示效果</li>\\n</ul>\"}")
export { comp, data }

if (import.meta.webpackHot) {
  import.meta.webpackHot.accept()
  if (__VUE_HMR_RUNTIME__.updatePageData) {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  }
}

if (import.meta.hot) {
  import.meta.hot.accept(({ data }) => {
    __VUE_HMR_RUNTIME__.updatePageData(data)
  })
}
