# ProductPageLayout.vue 使用说明

## 概述

ProductPageLayout.vue 是为VuePress 2.x项目优化的产品页面模板，专门设计用于展示产品信息，具有简洁的设计和良好的响应式效果。

## 页面结构

### 1. 产品标题区域
- **中文标题**：主要产品名称，使用超大字体（--font-size-4xl）突出显示
- **英文标题**：副标题，提供英文描述，字体较大（--font-size-xl）
- **设计特点**：使用品牌渐变色背景，添加文字阴影效果，增强视觉冲击力

### 2. 产品图片展示区域
- **主要产品图片**：超大尺寸展示图片（最大宽度95%，桌面端可达1000px）
- **产品列表**：支持多个产品的列表展示，图片尺寸优化（桌面端220x220px）
- **响应式设计**：自适应不同屏幕尺寸，移动端垂直布局
- **交互效果**：悬停缩放效果，增强用户体验

### 3. 详细内容区域（可选）
- **Markdown内容**：支持完整的Markdown格式
- **可控制显示**：通过配置控制是否显示

## Frontmatter 配置

```yaml
---
layout: ProductPageLayout
pageTitle: 页面标题
productTitleCn: 中文产品标题
productTitleEn: English Product Title
heroImage:
  src: 主要产品图片URL（建议高分辨率图片）
  alt: 图片描述
productList:
  - titleCn: 产品1中文名称
    titleEn: Product 1 English Name
    image: 产品1图片URL（建议正方形比例）
  - titleCn: 产品2中文名称
    titleEn: Product 2 English Name
    image: 产品2图片URL（建议正方形比例）
productFeatures:
  - 高清液晶显示屏，清晰可见
  - 蓝牙4.0连接，稳定可靠
  - IP67防水防尘，适应恶劣环境
  - 超长续航，低功耗设计
  - 智能防盗系统，安全保障
backUrl: /products/
showDetailContent: true
showMoreContent: true
moreContentUrl: /products/
---
```

## 配置参数说明

### 必需参数
- `layout: ProductPageLayout` - 指定使用产品页面布局
- `productTitleCn` - 中文产品标题（将以超大字体显示）
- `productTitleEn` - 英文产品标题（副标题）

### 可选参数
- `pageTitle` - 页面标题（用于浏览器标题栏）
- `heroImage` - 主要展示图片（推荐使用高分辨率图片）
  - `src` - 图片URL（建议使用HTTPS）
  - `alt` - 图片描述
- `productList` - 产品列表数组
  - `titleCn` - 产品中文名称
  - `titleEn` - 产品英文名称
  - `image` - 产品图片URL（建议正方形比例，最小200x200px）
- `productFeatures` - 产品特点列表数组
  - 格式：字符串数组，每个元素为一个产品特点描述
  - 建议3-5个特点，每个特点描述简洁明了（不超过20个字符）
- `backUrl` - 返回按钮链接（默认：/products/）
- `showDetailContent` - 是否显示详细内容区域（默认：true）
- `showMoreContent` - 是否显示"更多内容"按钮（默认：false）
- `moreContentUrl` - "更多内容"按钮链接

## 响应式设计

### 大屏幕（≥1200px）
- 主要产品图片最大宽度：1000px
- 产品列表项图片尺寸：220x220px
- 产品标题使用--font-size-4xl

### 桌面端（768px-1199px）
- 主要产品图片最大宽度：900px
- 产品列表项使用水平布局，图片尺寸：200x200px
- 产品标题使用--font-size-4xl

### 移动端（<480px）
- 主要产品图片占满宽度（100%）
- 产品列表项使用垂直布局，图片尺寸：140x140px
- 产品标题使用--font-size-3xl
- 紧凑的布局和间距

## 样式特点

### 设计原则
- **简洁设计**：避免过多文字描述，突出产品展示
- **品牌一致性**：使用双马智能科技品牌色彩
- **用户体验**：清晰的层次结构和导航

### 视觉效果
- **渐变背景**：标题区域使用品牌渐变色
- **文字阴影**：主标题添加文字阴影效果，增强视觉层次
- **阴影效果**：卡片和图片使用多层次阴影（--shadow-lg, --shadow-xl）
- **悬停效果**：主图片和产品项支持缩放动画（scale 1.03/1.05）
- **圆角设计**：统一的圆角风格，主图片使用--radius-xl
- **过渡动画**：所有交互元素都有流畅的过渡效果（0.3s-0.4s）

## 使用示例

参考 `docs/products/new/README.md` 文件，该文件展示了完整的配置示例。

## 技术要求

- VuePress 2.x
- Vue 3.x
- SCSS支持
- 响应式设计支持

## 浏览器兼容性

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 移动端浏览器
- 支持CSS Grid和Flexbox

## 注意事项

1. **图片要求**：
   - 主要产品图片建议使用高分辨率图片（最小800x600px）
   - 产品列表图片建议使用正方形比例（最小200x200px）
   - 所有图片URL应使用HTTPS协议
   - 建议使用WebP格式以优化加载速度

2. **性能优化**：
   - 产品列表建议不超过20个项目以保持页面性能
   - 图片应进行适当压缩，建议单张图片不超过500KB

3. **内容建议**：
   - 中文标题应保持简洁有力，建议不超过10个字符
   - 英文标题建议不超过30个字符
   - 产品名称避免过长，影响布局美观

4. **用户体验**：
   - 主要产品图片应清晰展示产品细节
   - 产品列表图片应保持一致的风格和质量
   - 确保所有图片都有适当的alt属性以提升可访问性
