<template>
  <div class="mobile-page product-page">
    <!-- 移动端导航头部 -->
    <MobileHeader
      :title="pageTitle"
      :show-back="true"
      back-url="/"
      :show-logo="false"
      :show-home="true"
    />
    
    <!-- 页面内容 -->
    <div class="page-content">
      <!-- 英雄区域 -->
      <section class="product-hero">
        <div class="hero-content">
          <div class="hero-image">
            <img 
              src="https://img-public.hui1688.cn/shuangma/thumb_6310248de1640.jpg" 
              alt="双马智能科技-电动车仪表专业生产制造商" 
              class="hero-img"
            />
          </div>
          <div class="hero-text">
            <h1 class="hero-title">双马智能科技-电动车仪表专业生产制造商</h1>
          </div>
        </div>
      </section>
      
      <!-- 产品分类导航 -->
      <section class="product-categories">
        <div class="mobile-container">
          <div class="categories-grid">
            <!-- 本月新品 -->
            <NavigationCard
              title="本月新品"
              subtitle="New products this month"
              icon-text="🆕"
              icon-bg-color="linear-gradient(135deg, #4299e1 0%, #3182ce 100%)"
              to="/products/new/"
              variant="default"
              size="normal"
              :show-arrow="true"
            />
            
            <!-- 新品发布 -->
            <NavigationCard
              title="新品发布"
              subtitle="New product release"
              icon-text="🚀"
              icon-bg-color="linear-gradient(135deg, #48bb78 0%, #38a169 100%)"
              to="/products/release/"
              variant="default"
              size="normal"
              :show-arrow="true"
            />
            
            <!-- 电摩系列 -->
            <NavigationCard
              title="电摩系列"
              subtitle="Electric motorcycle series"
              icon-text="🏍️"
              icon-bg-color="linear-gradient(135deg, #9f7aea 0%, #805ad5 100%)"
              to="/electric-series/"
              variant="default"
              size="normal"
              :show-arrow="true"
            />
            
            <!-- 国标系列 -->
            <NavigationCard
              title="国标系列"
              subtitle="National Standard series"
              icon-text="🏆"
              icon-bg-color="linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)"
              to="/national-series/"
              variant="default"
              size="normal"
              :show-arrow="true"
            />
            
            <!-- LCD系列 -->
            <NavigationCard
              title="LCD系列"
              subtitle="LCD series"
              icon-text="📱"
              icon-bg-color="linear-gradient(135deg, #38b2ac 0%, #**********%)"
              to="/products/lcd/"
              variant="default"
              size="normal"
              :show-arrow="true"
            />
          </div>
        </div>
      </section>
    </div>
    
    <!-- 页脚 -->
    <MobileFooter 
      :show-back-to-top="true"
      :quick-links="footerLinks"
      copyright-text="© 2022-2023 SPINRED 版权所有"
      company-info="无锡市双马智能科技有限公司"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { usePageFrontmatter } from 'vuepress/client'
import MobileFooter from '../components/MobileFooter.vue'
import MobileHeader from '../components/MobileHeader.vue'
import NavigationCard from '../components/NavigationCard.vue'

// 获取页面 frontmatter
const frontmatter = usePageFrontmatter()

// 计算页面标题
const pageTitle = computed(() => {
  return frontmatter.value.title || '产品展示'
})

// 页脚快速链接
const footerLinks = [
  { text: '返回首页', url: '/' },
  { text: '公司简介', url: '/company/' },
  { text: '生产车间', url: '/workshop/' },
  { text: '资质证书', url: '/qualification/' }
]
</script>

<style lang="scss" scoped>
@use '../styles/mobile.scss' as mobile;

.product-page {
  background: var(--brand-secondary);
  min-height: 100vh;
}

.product-hero {
  background: var(--brand-secondary);
  color: var(--brand-white);
  padding: var(--spacing-8) var(--spacing-4) var(--spacing-6);
  position: relative;
  overflow: hidden;
  
  .hero-content {
    max-width: 400px;
    margin: 0 auto;
    text-align: center;
  }
  
  .hero-image {
    margin-bottom: var(--spacing-6);
    position: relative;
    
    .hero-img {
      width: 100%;
      max-width: 300px;
      height: auto;
      border-radius: var(--radius-xl);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      filter: brightness(1.1) contrast(1.1);
    }
  }
  
  .hero-text {
    .hero-title {
      font-size: var(--font-size-xl);
      font-weight: 600;
      margin: 0;
      line-height: 1.4;
      letter-spacing: 1px;
      
      @include mobile.respond-to('md') {
        font-size: var(--font-size-2xl);
      }
    }
  }
  
  // 添加一些装饰性元素
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(229, 62, 62, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.product-categories {
  background: var(--brand-white);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  margin-top: -var(--spacing-4);
  position: relative;
  z-index: 2;
  padding: var(--spacing-8) 0 var(--spacing-6);
  
  .categories-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    
    @include mobile.respond-to('sm') {
      grid-template-columns: repeat(2, 1fr);
    }

    @include mobile.respond-to('lg') {
      grid-template-columns: repeat(3, 1fr);
      gap: var(--spacing-8);
    }
  }
}

// 响应式调整
@media (max-width: 480px) {
  .product-hero {
    padding: var(--spacing-6) var(--spacing-3) var(--spacing-4);
    
    .hero-text {
      .hero-title {
        font-size: var(--font-size-lg);
      }
    }
  }
  
  .product-categories {
    padding: var(--spacing-6) 0 var(--spacing-4);
    
    .categories-grid {
      gap: var(--spacing-4);
    }
  }
}

// 进入动画
.product-hero {
  animation: fadeIn 1s ease-out;
  
  .hero-image {
    animation: scaleIn 0.8s ease-out 0.3s both;
  }
  
  .hero-text {
    animation: fadeInUp 0.8s ease-out 0.5s both;
  }
}

.product-categories {
  animation: slideInUp 0.8s ease-out 0.4s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 卡片悬停效果增强
.categories-grid :deep(.nav-card) {
  transition: all var(--transition-normal);
  
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}
</style>
